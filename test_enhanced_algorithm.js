// Test the enhanced humanization algorithm
const fs = require('fs');

function testEnhancedAlgorithm() {
  console.log('=== TESTING ENHANCED ALGORITHM ===\n');
  
  // Test cases that were problematic
  const testCases = [
    "Imagine two scenarios:",
    "garbage in, garbage out. ",
    "This is a very good example of how AI systems work.",
    "The system will use advanced algorithms to make better results."
  ];
  
  // Simulate the enhanced processing functions
  function fixPunctuationIssues(text) {
    let result = text;
    
    // Fix double punctuation (e.g., ":." -> ":")
    result = result.replace(/([:.!?])\./g, '$1');
    
    // Fix extra spaces before punctuation
    result = result.replace(/\s+([.!?:;,])/g, '$1');
    
    // Fix multiple spaces
    result = result.replace(/\s{2,}/g, ' ');
    
    // Fix space before closing quotes
    result = result.replace(/\s+"/g, '"');
    result = result.replace(/"\s+/g, '" ');
    
    return result;
  }
  
  function finalCleanup(text) {
    let result = text;
    
    // Final punctuation cleanup
    result = result.replace(/([:.!?])\./g, '$1');
    result = result.replace(/\s+([.!?:;,])/g, '$1');
    result = result.replace(/\s{2,}/g, ' ');
    
    // Fix sentence endings
    result = result.replace(/\.\s*\.\s*/g, '. ');
    result = result.replace(/([.!?])\s*([A-Z])/g, '$1 $2');
    
    // Ensure proper spacing after punctuation
    result = result.replace(/([.!?])([A-Z])/g, '$1 $2');
    
    return result.trim();
  }
  
  function applyContextualRefinement(text) {
    let result = text;
    
    // Fix common word combination issues
    const problematicPatterns = [
      { pattern: /\b(deliver|present|offer)\s+me\b/gi, replacement: 'give me' },
      { pattern: /\bfor this reason\b/gi, replacement: 'therefore' },
      { pattern: /\btoobtainher\b/gi, replacement: 'together' },
      { pattern: /\btarobtained\b/gi, replacement: 'targeted' },
      { pattern: /\bpaimplement\b/gi, replacement: 'pause' },
      { pattern: /\bunfocimplementd\b/gi, replacement: 'unfocused' },
      { pattern: /\bprimarily advanced\b/gi, replacement: 'most advanced' },
      { pattern: /\bnearly all advanced\b/gi, replacement: 'most advanced' }
    ];
    
    problematicPatterns.forEach(({ pattern, replacement }) => {
      result = result.replace(pattern, replacement);
    });
    
    return result;
  }
  
  function processText(text) {
    let result = text;
    
    // Step 1: Fix punctuation issues
    result = fixPunctuationIssues(result);
    
    // Step 2: Apply contextual refinement
    result = applyContextualRefinement(result);
    
    // Step 3: Final cleanup
    result = finalCleanup(result);
    
    return result;
  }
  
  // Test each case
  testCases.forEach((testCase, index) => {
    console.log(`Test Case ${index + 1}:`);
    console.log(`Input:  "${testCase}"`);
    
    const processed = processText(testCase);
    console.log(`Output: "${processed}"`);
    
    // Check for improvements
    const hasDoubleColon = testCase.includes(':.');
    const fixedDoubleColon = !processed.includes(':.');
    const hasExtraSpace = testCase.includes('. ');
    const fixedExtraSpace = !processed.includes('. ') || processed.trim().endsWith('.');
    
    console.log(`Improvements:`);
    if (hasDoubleColon && fixedDoubleColon) {
      console.log('  ✓ Fixed double punctuation (:.)');
    }
    if (hasExtraSpace && fixedExtraSpace) {
      console.log('  ✓ Fixed extra spacing');
    }
    if (processed !== testCase) {
      console.log('  ✓ Text was processed');
    } else {
      console.log('  - No changes needed');
    }
    
    console.log('');
  });
  
  // Test problematic words from the files
  const problematicWords = [
    "deliver me content ideas",
    "paimplement",
    "tarobtained", 
    "toobtainher",
    "unfocimplementd",
    "primarily advanced",
    "for this reason"
  ];
  
  console.log('=== TESTING PROBLEMATIC WORD FIXES ===\n');
  
  problematicWords.forEach((word, index) => {
    console.log(`Problematic Word ${index + 1}:`);
    console.log(`Input:  "${word}"`);
    
    const processed = processText(word);
    console.log(`Output: "${processed}"`);
    
    if (processed !== word) {
      console.log('  ✓ Fixed problematic word');
    } else {
      console.log('  - No fix applied');
    }
    
    console.log('');
  });
  
  console.log('=== TEST COMPLETE ===');
}

// Run the test
testEnhancedAlgorithm();
