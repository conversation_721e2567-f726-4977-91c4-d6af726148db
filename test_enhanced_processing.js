// Test the enhanced processing with actual input/output comparison
const fs = require('fs');
const path = require('path');

// Read input and problematic output
const inputPath = path.join(__dirname, 'input_output', 'in.txt');
const outputPath = path.join(__dirname, 'input_output', 'out.txt');

const inputText = fs.readFileSync(inputPath, 'utf8');
const problematicOutput = fs.readFileSync(outputPath, 'utf8');

console.log('=== ANALYZING PROBLEMATIC OUTPUT ===\n');

// Analyze issues in the problematic output
function analyzeIssues(text) {
  const issues = [];
  
  // Check for excessive transition words
  const excessiveTransitions = text.match(/Furthermore, additionally, on the other hand|Furthermore, hence|Given these circumstances, meanwhile|It should be noted that|It is worth mentioning that|Notably, one might argue that|In contrast, it should be noted that/gi);
  if (excessiveTransitions) {
    issues.push(`Excessive transitions found: ${excessiveTransitions.length} instances`);
  }
  
  // Check for lost capitalization
  const lostCapitalization = text.match(/\bai\b|\bllm\b|\bllms\b|\bapi\b|\burl\b/g);
  if (lostCapitalization) {
    issues.push(`Lost capitalization: ${lostCapitalization.length} instances`);
  }
  
  // Check for awkward replacements
  const awkwardReplacements = text.match(/via the topic means|which aspect|through the item process|at the moment working|during which period|in which aspect way|the topic you'll learn|plus through the topic process/gi);
  if (awkwardReplacements) {
    issues.push(`Awkward replacements: ${awkwardReplacements.length} instances`);
  }
  
  // Check for broken headers
  const brokenHeaders = text.match(/[a-z]### |[a-z]## /g);
  if (brokenHeaders) {
    issues.push(`Broken headers: ${brokenHeaders.length} instances`);
  }
  
  // Check for run-on sentences
  const runOnSentences = text.split(/[.!?]+/).filter(sentence => {
    const words = sentence.trim().split(/\s+/);
    return words.length > 50; // Very long sentences
  });
  if (runOnSentences.length > 0) {
    issues.push(`Run-on sentences: ${runOnSentences.length} instances`);
  }
  
  return issues;
}

const issues = analyzeIssues(problematicOutput);
console.log('Issues found in problematic output:');
issues.forEach((issue, index) => {
  console.log(`${index + 1}. ${issue}`);
});

// Show examples of problematic text
console.log('\n=== EXAMPLES OF PROBLEMATIC TEXT ===\n');

// Example 1: Excessive transitions
const excessiveExample = problematicOutput.match(/Furthermore, additionally, on the other hand, \*\*unlocking true ai potential\*\*[^.]+\./i);
if (excessiveExample) {
  console.log('Excessive transitions example:');
  console.log(excessiveExample[0]);
  console.log();
}

// Example 2: Lost capitalization
const capitalizationExample = problematicOutput.match(/[^.!?]*\bai\b[^.!?]*[.!?]/i);
if (capitalizationExample) {
  console.log('Lost capitalization example:');
  console.log(capitalizationExample[0]);
  console.log();
}

// Example 3: Awkward replacements
const awkwardExample = problematicOutput.match(/[^.!?]*via the topic means[^.!?]*[.!?]/i);
if (awkwardExample) {
  console.log('Awkward replacement example:');
  console.log(awkwardExample[0]);
  console.log();
}

// Example 4: Broken headers
const brokenHeaderExample = problematicOutput.match(/[a-z]### [^\\n]+/);
if (brokenHeaderExample) {
  console.log('Broken header example:');
  console.log(brokenHeaderExample[0]);
  console.log();
}

console.log('=== COMPARISON WITH ORIGINAL ===\n');

// Compare structure preservation
const originalLines = inputText.split('\n');
const problematicLines = problematicOutput.split('\n');

console.log(`Original lines: ${originalLines.length}`);
console.log(`Problematic output lines: ${problematicLines.length}`);

// Count headers in both
const originalHeaders = originalLines.filter(line => /^#{1,6}\s/.test(line.trim())).length;
const problematicHeaders = problematicLines.filter(line => /^#{1,6}\s/.test(line.trim())).length;

console.log(`Original headers: ${originalHeaders}`);
console.log(`Problematic headers: ${problematicHeaders}`);

// Show first few lines comparison
console.log('\nFirst 5 lines comparison:');
console.log('ORIGINAL:');
originalLines.slice(0, 5).forEach((line, i) => {
  console.log(`${i + 1}: ${line}`);
});

console.log('\nPROBLEMATIC:');
problematicLines.slice(0, 5).forEach((line, i) => {
  console.log(`${i + 1}: ${line}`);
});

console.log('\n=== ENHANCED ALGORITHM GOALS ===\n');
console.log('1. Preserve all headers and structure');
console.log('2. Maintain proper capitalization (AI, LLM, etc.)');
console.log('3. Reduce excessive transition words');
console.log('4. Avoid awkward word replacements');
console.log('5. Keep natural sentence flow');
console.log('6. Preserve line breaks and formatting');

console.log('\n=== TEST COMPLETE ===');
