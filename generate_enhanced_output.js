// Generate enhanced output using the improved algorithm
const fs = require('fs');
const path = require('path');

// Read the input text
const inputPath = path.join(__dirname, 'input_output', 'in.txt');
const inputText = fs.readFileSync(inputPath, 'utf8');

console.log('=== GENERATING ENHANCED OUTPUT ===\n');

function enhancedHumanization(text) {
  // Protected terms that should never be changed
  const protectedTerms = [
    'AI', 'API', 'URL', 'HTML', 'CSS', 'JavaScript', 'TypeScript', 'React', 'Next.js',
    'LLM', 'LLMs', 'GPT', 'ChatGPT', 'OpenAI', 'GitHub', 'LinkedIn', 'B2B', 'UI', 'UX',
    'CEO', 'CTO', 'CFO', 'HR', 'IT', 'PR', 'SEO', 'SEM', 'ROI', 'KPI', 'FAQ', 'PDF'
  ];

  // High-quality synonym dictionary
  const synonyms = {
    'very': ['extremely', 'quite', 'really', 'remarkably'],
    'good': ['excellent', 'great', 'fine', 'outstanding'],
    'bad': ['poor', 'inadequate', 'subpar', 'disappointing'],
    'big': ['large', 'substantial', 'significant', 'considerable'],
    'small': ['compact', 'modest', 'minor', 'limited'],
    'important': ['crucial', 'vital', 'essential', 'significant'],
    'new': ['recent', 'fresh', 'modern', 'contemporary'],
    'old': ['previous', 'former', 'earlier', 'established'],
    'make': ['create', 'produce', 'generate', 'develop'],
    'get': ['obtain', 'acquire', 'receive', 'secure'],
    'use': ['utilize', 'employ', 'apply', 'implement'],
    'show': ['demonstrate', 'reveal', 'display', 'illustrate'],
    'give': ['provide', 'offer', 'supply', 'deliver'],
    'help': ['assist', 'support', 'aid', 'facilitate'],
    'find': ['discover', 'locate', 'identify', 'uncover'],
    'think': ['believe', 'consider', 'feel', 'suppose'],
    'know': ['understand', 'realize', 'recognize', 'comprehend'],
    'work': ['function', 'operate', 'perform', 'execute'],
    'different': ['various', 'diverse', 'distinct', 'alternative'],
    'same': ['identical', 'similar', 'equivalent', 'comparable'],
    'many': ['numerous', 'multiple', 'various', 'several'],
    'much': ['considerable', 'substantial', 'significant', 'extensive'],
    'first': ['initial', 'primary', 'opening', 'leading'],
    'last': ['final', 'concluding', 'ultimate', 'closing'],
    'next': ['following', 'subsequent', 'upcoming', 'ensuing'],
    'best': ['optimal', 'finest', 'superior', 'top'],
    'better': ['improved', 'enhanced', 'superior', 'refined'],
    'great': ['excellent', 'outstanding', 'remarkable', 'exceptional'],
    'high': ['elevated', 'advanced', 'superior', 'top-tier'],
    'low': ['reduced', 'minimal', 'basic', 'limited'],
    'fast': ['rapid', 'quick', 'swift', 'speedy'],
    'slow': ['gradual', 'steady', 'measured', 'deliberate'],
    'easy': ['simple', 'straightforward', 'effortless', 'uncomplicated'],
    'hard': ['challenging', 'difficult', 'complex', 'demanding'],
    'long': ['extended', 'lengthy', 'prolonged', 'comprehensive'],
    'short': ['brief', 'concise', 'compact', 'succinct'],
    'clear': ['obvious', 'evident', 'apparent', 'transparent'],
    'simple': ['straightforward', 'basic', 'uncomplicated', 'elementary']
  };

  // Step 1: Preserve structure
  const lines = text.split('\n').map(line => {
    const trimmed = line.trim();
    const indentation = line.match(/^(\s*)/)?.[1] || '';
    
    return {
      content: trimmed,
      isHeader: /^#{1,6}\s/.test(trimmed),
      isEmpty: trimmed.length === 0,
      isListItem: /^[-*+]\s/.test(trimmed) || /^\d+\.\s/.test(trimmed),
      indentation,
      originalLine: line
    };
  });

  // Step 2: Process content lines
  const processedLines = lines.map(line => {
    if (line.isHeader || line.isEmpty || line.isListItem) {
      return line; // Keep unchanged
    }
    
    let content = line.content;
    
    // Skip very short content
    if (content.length < 10) {
      return line;
    }
    
    // Step 2a: Protect important terms
    const protectionMap = new Map();
    protectedTerms.forEach((term, index) => {
      const placeholder = `PROTECTED${index}TERM`;
      const regex = new RegExp(`\\b${term}\\b`, 'g');
      content = content.replace(regex, placeholder);
      protectionMap.set(placeholder, term);
    });
    
    // Step 2b: Apply synonym replacement (conservative approach)
    const sentences = content.split(/(?<=[.!?])\s+/).filter(s => s.trim().length > 0);
    
    const processedSentences = sentences.map((sentence, sentenceIndex) => {
      let processed = sentence;
      
      // Apply synonym replacement with 25% chance per word
      Object.entries(synonyms).forEach(([word, replacements]) => {
        if (Math.random() < 0.25) {
          const regex = new RegExp(`\\b${word}\\b`, 'gi');
          if (regex.test(processed)) {
            const replacement = replacements[Math.floor(Math.random() * replacements.length)];
            processed = processed.replace(regex, replacement);
          }
        }
      });
      
      // Very rarely add natural transitions (5% chance, only for longer content)
      if (sentenceIndex > 0 && sentences.length > 2 && processed.length > 30 && Math.random() < 0.05) {
        const naturalTransitions = ['Additionally', 'Furthermore', 'Moreover', 'However'];
        const transition = naturalTransitions[Math.floor(Math.random() * naturalTransitions.length)];
        
        // Only add if sentence doesn't already start with a transition
        if (!/^(Additionally|Furthermore|Moreover|However|Therefore|Consequently|Meanwhile|Similarly)/i.test(processed.trim())) {
          processed = transition + ', ' + processed.toLowerCase();
        }
      }
      
      // Ensure proper capitalization
      processed = processed.charAt(0).toUpperCase() + processed.slice(1);
      
      return processed;
    });
    
    content = processedSentences.join(' ');
    
    // Step 2c: Restore protected terms
    protectionMap.forEach((original, placeholder) => {
      const regex = new RegExp(placeholder, 'g');
      content = content.replace(regex, original);
    });
    
    return { ...line, content };
  });

  // Step 3: Reconstruct text
  return processedLines.map(line => {
    if (line.isEmpty) return '';
    if (line.isHeader || line.isListItem) return line.originalLine;
    return line.indentation + line.content;
  }).join('\n');
}

// Generate enhanced output
const enhancedOutput = enhancedHumanization(inputText);

// Save the enhanced output
const enhancedOutputPath = path.join(__dirname, 'input_output', 'enhanced_out.txt');
fs.writeFileSync(enhancedOutputPath, enhancedOutput, 'utf8');

console.log('Enhanced output generated and saved to:', enhancedOutputPath);
console.log('\nOutput preview (first 500 characters):');
console.log(enhancedOutput.substring(0, 500) + '...');

// Compare with original problematic output
const problematicOutputPath = path.join(__dirname, 'input_output', 'out.txt');
const problematicOutput = fs.readFileSync(problematicOutputPath, 'utf8');

console.log('\n=== COMPARISON ===');
console.log(`Original length: ${inputText.length}`);
console.log(`Problematic output length: ${problematicOutput.length}`);
console.log(`Enhanced output length: ${enhancedOutput.length}`);

// Count lines
console.log(`Original lines: ${inputText.split('\n').length}`);
console.log(`Problematic lines: ${problematicOutput.split('\n').length}`);
console.log(`Enhanced lines: ${enhancedOutput.split('\n').length}`);

// Count headers
const originalHeaders = inputText.split('\n').filter(line => /^#{1,6}\s/.test(line.trim())).length;
const problematicHeaders = problematicOutput.split('\n').filter(line => /^#{1,6}\s/.test(line.trim())).length;
const enhancedHeaders = enhancedOutput.split('\n').filter(line => /^#{1,6}\s/.test(line.trim())).length;

console.log(`Original headers: ${originalHeaders}`);
console.log(`Problematic headers: ${problematicHeaders}`);
console.log(`Enhanced headers: ${enhancedHeaders}`);

console.log('\n=== GENERATION COMPLETE ===');
