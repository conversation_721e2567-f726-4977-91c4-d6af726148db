'use client';

import { useState } from 'react';
import { processTextDirectly } from '@/lib/textProcessor';
import { ProcessingOptions as ProcessingOptionsType } from '@/types';
import Header from '@/components/Header';
import TextInput from '@/components/TextInput';
import ProcessingOptions from '@/components/ProcessingOptions';
import OutputDisplay from '@/components/OutputDisplay';
import DetectionScore from '@/components/DetectionScore';
import Testimonials from '@/components/Testimonials';
import FAQ from '@/components/FAQ';
import AuthoritativeContent from '@/components/AuthoritativeContent';
import Footer from '@/components/Footer';
import { ProcessingResult } from '@/types';
import { analytics } from '@/lib/analytics';

export default function Home() {
  const [inputText, setInputText] = useState('');
  const [result, setResult] = useState<ProcessingResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingOptions, setProcessingOptions] = useState({
    intensity: 'medium',
    style: 'academic',
    preserveFormat: true,
    addVariations: false
  });

  const handleProcess = async () => {
    if (!inputText.trim()) return;

    setIsProcessing(true);
    try {
      // Add a small delay to show processing state
      await new Promise(resolve => setTimeout(resolve, 1000));

      const options: ProcessingOptionsType = {
        intensity: processingOptions.intensity as ProcessingOptionsType['intensity'],
        style: processingOptions.style as ProcessingOptionsType['style'],
        preserveFormat: processingOptions.preserveFormat,
        addVariations: processingOptions.addVariations
      };

      const startTime = Date.now();
      const data = processTextDirectly(inputText, options);
      const processingTime = Date.now() - startTime;

      setResult(data);

      // Track analytics
      analytics.trackTextProcessed({
        style: options.style,
        intensity: options.intensity,
        textLength: inputText.length,
        processingTime: processingTime,
        hasVariations: options.addVariations
      });

    } catch (error) {
      console.error('Processing failed:', error);

      // Track error
      analytics.trackError('processing_failed', (error as Error).message);

      alert('Error processing text: ' + (error as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <Header />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          <div className="lg:col-span-2 space-y-6">
            <TextInput
              value={inputText}
              onChange={setInputText}
              onProcess={handleProcess}
              isProcessing={isProcessing}
            />

            <OutputDisplay
              originalText={inputText}
              result={result}
              isProcessing={isProcessing}
            />
          </div>

          <div className="space-y-6">
            <ProcessingOptions
              options={processingOptions}
              onChange={setProcessingOptions}
            />

            <DetectionScore result={result} />
          </div>
        </div>

        {/* Authoritative Content Section */}
        <AuthoritativeContent />

        {/* Testimonials Section */}
        <Testimonials />

        {/* FAQ Section */}
        <FAQ />
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}