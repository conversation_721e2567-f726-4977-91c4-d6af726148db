// Test script to verify the enhanced humanization algorithm
const fs = require('fs');
const path = require('path');

// Mock the required modules for testing
const mockProcessingOptions = {
  intensity: 'medium',
  style: 'balanced',
  preserveFormat: true,
  addVariations: false
};

// Read the input text
const inputPath = path.join(__dirname, 'input_output', 'in.txt');
const inputText = fs.readFileSync(inputPath, 'utf8');

console.log('=== TESTING ENHANCED HUMANIZATION ALGORITHM ===\n');
console.log('Input text length:', inputText.length);
console.log('Input preview:', inputText.substring(0, 200) + '...\n');

// Test the structure preservation function
function preserveStructure(text) {
  const lines = text.split('\n').map(line => {
    const trimmed = line.trim();
    const indentation = line.match(/^(\s*)/)?.[1] || '';
    
    return {
      content: trimmed,
      isHeader: /^#{1,6}\s/.test(trimmed),
      isEmpty: trimmed.length === 0,
      isListItem: /^[-*+]\s/.test(trimmed) || /^\d+\.\s/.test(trimmed),
      indentation,
      originalLine: line
    };
  });
  
  return {
    lines,
    hasHeaders: lines.some(l => l.isHeader),
    hasLists: lines.some(l => l.isListItem)
  };
}

function reconstructText(lines) {
  return lines.map(line => {
    if (line.isEmpty) return '';
    if (line.isHeader || line.isListItem) return line.originalLine;
    return line.indentation + line.content;
  }).join('\n');
}

// Test structure preservation
const { lines, hasHeaders, hasLists } = preserveStructure(inputText);
console.log('Structure analysis:');
console.log('- Has headers:', hasHeaders);
console.log('- Has lists:', hasLists);
console.log('- Total lines:', lines.length);
console.log('- Header lines:', lines.filter(l => l.isHeader).length);
console.log('- List item lines:', lines.filter(l => l.isListItem).length);
console.log('- Empty lines:', lines.filter(l => l.isEmpty).length);
console.log('- Content lines:', lines.filter(l => !l.isHeader && !l.isEmpty && !l.isListItem).length);

// Test reconstruction
const reconstructed = reconstructText(lines);
console.log('\nReconstruction test:');
console.log('- Original length:', inputText.length);
console.log('- Reconstructed length:', reconstructed.length);
console.log('- Match:', inputText === reconstructed ? 'PASS' : 'FAIL');

// Test protected terms
const protectedTerms = [
  'AI', 'API', 'URL', 'HTML', 'CSS', 'JavaScript', 'TypeScript', 'React', 'Next.js',
  'LLM', 'LLMs', 'GPT', 'ChatGPT', 'OpenAI', 'GitHub', 'LinkedIn', 'B2B', 'UI', 'UX'
];

function testProtectedTerms(text) {
  const found = [];
  protectedTerms.forEach(term => {
    const regex = new RegExp(`\\b${term}\\b`, 'g');
    const matches = text.match(regex);
    if (matches) {
      found.push({ term, count: matches.length });
    }
  });
  return found;
}

const foundTerms = testProtectedTerms(inputText);
console.log('\nProtected terms found in input:');
foundTerms.forEach(({ term, count }) => {
  console.log(`- ${term}: ${count} occurrences`);
});

// Test enhanced synonym replacement (simplified version)
function testSynonymReplacement(text) {
  const synonyms = {
    'very': ['extremely', 'quite', 'really', 'remarkably'],
    'good': ['excellent', 'great', 'fine', 'outstanding'],
    'important': ['crucial', 'vital', 'essential', 'significant'],
    'new': ['recent', 'fresh', 'novel', 'modern']
  };
  
  let result = text;
  let replacements = 0;
  
  Object.entries(synonyms).forEach(([word, replacementList]) => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = result.match(regex);
    if (matches && Math.random() < 0.3) {
      const replacement = replacementList[Math.floor(Math.random() * replacementList.length)];
      result = result.replace(regex, replacement);
      replacements++;
    }
  });
  
  return { result, replacements };
}

// Test on a sample paragraph
const sampleParagraph = "This is a very good example of new and important content that shows how AI systems work.";
const { result: processedSample, replacements } = testSynonymReplacement(sampleParagraph);

console.log('\nSynonym replacement test:');
console.log('Original:', sampleParagraph);
console.log('Processed:', processedSample);
console.log('Replacements made:', replacements);

console.log('\n=== TEST COMPLETE ===');
