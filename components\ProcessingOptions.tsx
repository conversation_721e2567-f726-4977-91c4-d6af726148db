'use client';

import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Settings, Zap, Palette, Shield } from 'lucide-react';
import { analytics } from '@/lib/analytics';

interface ProcessingOptionsProps {
  options: {
    intensity: string;
    style: string;
    preserveFormat: boolean;
    addVariations: boolean;
  };
  onChange: (options: any) => void;
}

export default function ProcessingOptions({ options, onChange }: ProcessingOptionsProps) {
  const intensityValue = options.intensity === 'light' ? 25 : options.intensity === 'medium' ? 50 : 75;

  const handleIntensityChange = (value: number[]) => {
    const intensity = value[0] <= 33 ? 'light' : value[0] <= 66 ? 'medium' : 'heavy';
    onChange({ ...options, intensity });
  };

  return (
    <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10">
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
        <Settings className="w-5 h-5" />
        Processing Options
      </h3>
      
      <div className="space-y-6">
        <div>
          <Label className="text-white mb-3 block flex items-center gap-2">
            <Zap className="w-4 h-4 text-yellow-400" />
            Transformation Intensity
          </Label>
          <div className="px-2">
            <Slider
              value={[intensityValue]}
              onValueChange={handleIntensityChange}
              max={100}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-400 mt-2">
              <span>Light</span>
              <span>Medium</span>
              <span>Heavy</span>
            </div>
          </div>
          <p className="text-xs text-gray-400 mt-2">
            {options.intensity === 'light' && 'Minimal changes, preserves original structure'}
            {options.intensity === 'medium' && 'Balanced transformation with good readability'}
            {options.intensity === 'heavy' && 'Maximum humanization, significant restructuring'}
          </p>
        </div>

        <div>
          <Label className="text-white mb-3 block flex items-center gap-2">
            <Palette className="w-4 h-4 text-purple-400" />
            Writing Style
          </Label>
          <Select value={options.style} onValueChange={(value) => {
            analytics.trackStyleChange(options.style, value);
            onChange({ ...options, style: value });
          }}>
            <SelectTrigger className="bg-slate-800/50 border-slate-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700 text-white">
              <SelectItem value="balanced" className="text-white hover:bg-blue-600 focus:bg-blue-600">Balanced</SelectItem>
              <SelectItem value="formal" className="text-white hover:bg-blue-600 focus:bg-blue-600">Formal</SelectItem>
              <SelectItem value="casual" className="text-white hover:bg-blue-600 focus:bg-blue-600">Casual</SelectItem>
              <SelectItem value="academic" className="text-white hover:bg-blue-600 focus:bg-blue-600">Academic</SelectItem>
              <SelectItem value="creative" className="text-white hover:bg-blue-600 focus:bg-blue-600">Creative</SelectItem>
              <SelectItem value="technical" className="text-white hover:bg-blue-600 focus:bg-blue-600">Technical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-white flex items-center gap-2">
                <Shield className="w-4 h-4 text-green-400" />
                Preserve Formatting
              </Label>
              <p className="text-xs text-gray-400 mt-1">
                Keep original paragraph structure and formatting
              </p>
            </div>
            <Switch
              checked={options.preserveFormat}
              onCheckedChange={(checked) => onChange({ ...options, preserveFormat: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-white">Generate Variations</Label>
              <p className="text-xs text-gray-400 mt-1">
                Create multiple versions for comparison
              </p>
            </div>
            <Switch
              checked={options.addVariations}
              onCheckedChange={(checked) => {
                analytics.trackVariationsToggle(checked);
                onChange({ ...options, addVariations: checked });
              }}
            />
          </div>
        </div>
      </div>
    </Card>
  );
}